<script setup lang="ts">
import {ref} from 'vue'
import {useModal, useToast, VaButton, VaButtonToggle, VaCard, VaCardContent, VaModal} from 'vuestic-ui'
import {VehicleType} from './types'
import {useLocalStorage} from '@vueuse/core'
import {useVehicleTypes} from './composables/useVehicleTypes'
import VehicleTypeCards from './widgets/VehicleTypeCards.vue'
import VehicleTypesTable from './widgets/VehicleTypesTable.vue'
import EditVehicleTypeForm from './widgets/EditVehicleTypeForm.vue'

const doShowAsCards = useLocalStorage('vehicle-types-view', true)

const {vehicleTypes, add, update, remove, isLoading,  pagination, sorting} = useVehicleTypes()

const vehicleTypeToEdit = ref<VehicleType | null>(null)
const doShowVehicleTypeFormModal = ref(false)

const editVehicleType = (vehicleType: VehicleType) => {
    vehicleTypeToEdit.value = vehicleType
    doShowVehicleTypeFormModal.value = true
}

const createNewVehicleType = () => {
    vehicleTypeToEdit.value = null
    doShowVehicleTypeFormModal.value = true
}

const {init: notify} = useToast()

const onVehicleTypeSaved = async (vehicleType: VehicleType) => {
    doShowVehicleTypeFormModal.value = false
    if ('id' in vehicleType) {
        await update(vehicleType as VehicleType)
        notify({
            message: 'VehicleType updated',
            color: 'success',
        })
    } else {
        await add(vehicleType as VehicleType)
        notify({
            message: 'VehicleType created',
            color: 'success',
        })
    }
}

const {confirm} = useModal()

const onVehicleTypeDeleted = async (vehicleType: VehicleType) => {
    const response = await confirm({
        title: 'Delete Vehicle Type',
        message: `Are you sure you want to delete vehicle type "${vehicleType.name}"?`,
        okText: 'Delete',
        size: 'small',
        maxWidth: '380px',
    })

    if (!response) {
        return
    }

    await remove(vehicleType)
    notify({
        message: 'VehicleType deleted',
        color: 'success',
    })
}

const editFormRef = ref()

const beforeEditFormModalClose = async (hide: () => unknown) => {
    if (editFormRef.value.isFormHasUnsavedChanges) {
        const agreed = await confirm({
            maxWidth: '380px',
            message: 'Form has unsaved changes. Are you sure you want to close it?',
            size: 'small',
        })
        if (agreed) {
            hide()
        }
    } else {
        hide()
    }
}
</script>

<template>
    <h6 class="page-title">Vehicle Types</h6>

    <VaCard>
        <VaCardContent>
            <div class="flex flex-col md:flex-row gap-2 mb-2 justify-between">
                <div class="flex flex-col md:flex-row gap-2 justify-start">
                    <VaButtonToggle
                        v-model="doShowAsCards"
                        color="background-element"
                        border-color="background-element"
                        :options="[{ label: 'Cards', value: true }, { label: 'Table', value: false },]"
                    />
                </div>
                <VaButton icon="add" @click="createNewVehicleType">Vehicle Type</VaButton>
            </div>

            <VehicleTypeCards
                v-if="doShowAsCards"
                :vehicleTypes="vehicleTypes"
                :loading="isLoading"
                @edit="editVehicleType"
                @delete="onVehicleTypeDeleted"
            />
            <VehicleTypesTable
                v-else
                v-model:sort-by="sorting.sortBy"
                v-model:sorting-order="sorting.sortingOrder"
                v-model:pagination="pagination"
                :vehicleTypes="vehicleTypes"
                :loading="isLoading"
                @edit="editVehicleType"
                @delete="onVehicleTypeDeleted"
            />
        </VaCardContent>

        <VaModal
            v-slot="{ cancel, ok }"
            v-model="doShowVehicleTypeFormModal"
            size="small"
            mobile-fullscreen
            close-button
            stateful
            hide-default-actions
            :before-cancel="beforeEditFormModalClose"
        >
            <h1 v-if="vehicleTypeToEdit === null" class="va-h5 mb-4">Add Vehicle Type</h1>
            <h1 v-else class="va-h5 mb-4">Edit Vehicle Type</h1>
            <EditVehicleTypeForm
                ref="editFormRef"
                :vehicleType="vehicleTypeToEdit"
                :save-button-label="vehicleTypeToEdit === null ? 'Add' : 'Save'"
                @close="cancel"
                @save="(vehicleType) => {
                    onVehicleTypeSaved(vehicleType)
                    ok()
                }"
            />
        </VaModal>
    </VaCard>
</template>
