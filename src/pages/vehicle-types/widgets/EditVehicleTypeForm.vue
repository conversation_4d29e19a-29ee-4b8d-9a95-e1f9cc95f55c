<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {EmptyVehicleType, VehicleType} from '../types'

const props = defineProps<{
    vehicleType: VehicleType | null
    saveButtonLabel: string
}>()

defineEmits<{
    (event: 'save', vehicleType: VehicleType): void
    (event: 'close'): void
}>()

const defaultNewVehicleType: EmptyVehicleType = {
    name: '',
}

const newVehicleType = ref({...defaultNewVehicleType})

const isFormHasUnsavedChanges = computed(() => {
    return Object.keys(newVehicleType.value).some((key) => {
        return (
            newVehicleType.value[key as keyof EmptyVehicleType] !== (props.vehicleType ?? defaultNewVehicleType)?.[key as keyof EmptyVehicleType]
        )
    })
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.vehicleType,
    () => {
        if (!props.vehicleType) {
            newVehicleType.value = {...defaultNewVehicleType}
            return
        }

        newVehicleType.value = {
            name: props.vehicleType.name,
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <VaInput v-model="newVehicleType.name" label="Vehicle Type Name" :rules="[required]"/>
        <div class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && $emit('save', newVehicleType as VehicleType)">{{ saveButtonLabel }}</VaButton>
        </div>
    </VaForm>
</template>