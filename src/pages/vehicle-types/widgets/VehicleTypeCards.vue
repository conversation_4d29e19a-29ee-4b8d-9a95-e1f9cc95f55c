<script setup lang="ts">
import {type PropType} from 'vue'
import {type VehicleType} from '../types'
import {VaButton, VaCard, VaCardContent, VaDivider, VaInnerLoading} from "vuestic-ui";

defineProps({
    vehicleTypes: {
        type: Array as PropType<VehicleType[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
})

defineEmits<{
    (event: 'edit', vehicleType: VehicleType): void
    (event: 'delete', vehicleType: VehicleType): void
}>()

function formatDateTime(dateStr: string): string {
  const date = new Date(dateStr)
  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
}
</script>

<template>
    <VaInnerLoading
        v-if="vehicleTypes.length > 0 || loading"
        :loading="loading"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-h-[4rem]"
    >
        <VaCard
            v-for="vehicleType in vehicleTypes"
            :key="vehicleType.id"
            :style="{ '--va-card-outlined-border': '1px solid var(--va-background-element)' }"
            outlined
        >
            <VaCardContent class="flex flex-col h-full">
                <div class="flex justify-between">
                    <div class="text-[var(--va-secondary)]">Created:
                        {{ formatDateTime(vehicleType.createdAt) }}
                    </div>
                    <div class="text-[var(--va-secondary)]">Updated:
                        {{ formatDateTime(vehicleType.updatedAt) }}
                    </div>
                </div>
                <div class="flex flex-col items-center gap-4 grow">
                    <h4 class="va-h4 text-center self-stretch overflow-hidden line-clamp-2 text-ellipsis">
                        {{ vehicleType.name }}
                    </h4>
                    <!--                    <p class="text-[var(&#45;&#45;va-secondary)] text-center">-->
                    <!--                        Vehicle Type-->
                    <!--                    </p>-->
                </div>
                <VaDivider class="my-6"/>
                <div class="flex justify-between">
                    <VaButton preset="secondary" icon="mso-edit" color="secondary" @click="$emit('edit', vehicleType)"/>
                    <VaButton preset="secondary" icon="mso-delete" color="danger"
                              @click="$emit('delete', vehicleType)"/>
                </div>
            </VaCardContent>
        </VaCard>
    </VaInnerLoading>
    <div v-else class="p-4 flex justify-center items-center text-[var(--va-secondary)]">No vehicle types</div>
</template>
