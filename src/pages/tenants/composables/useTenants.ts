import { computed, ref, Ref, unref } from 'vue';
import { Pagination, Sorting } from '../../../data/pages/tenants';
import { Tenant } from '../types';
import { useTenantsStore } from '../../../stores/tenantStores';

const makePaginationRef = () => ref<Pagination>({ page: 1, perPage: 10, total: 0 });
const makeSortingRef = () => ref<Sorting>({ sortBy: 'createdAt', sortingOrder: 'DESC' });

export const useTenants = (options?: { sorting?: Ref<Sorting>; pagination?: Ref<Pagination> }) => {
    const isLoading = ref(false);
    const tenantsStore = useTenantsStore();

    const { sorting = makeSortingRef(), pagination = makePaginationRef() } = options ?? {};

    const fetch = async () => {
        isLoading.value = true;
        await tenantsStore.getAll({
            sorting: unref(sorting),
            pagination: unref(pagination),
        });
        pagination.value = tenantsStore.pagination;
        isLoading.value = false;
    };

    const tenants = computed(() => {
        const paginated = tenantsStore.items.slice(
            (pagination.value.page - 1) * pagination.value.perPage,
            pagination.value.page * pagination.value.perPage,
        );

        const getSortItem = (obj: any, sortBy: Sorting['sortBy']) => {
            if (sortBy === 'name') {
                return obj.name;
            }

            if (sortBy === 'updatedAt') {
                 return new Date(obj[sortBy]);
            }

            if (sortBy === 'createdAt') {
                return new Date(obj[sortBy]);
            }

            return obj[sortBy];
        };

        if (sorting.value.sortBy && sorting.value.sortingOrder) {
            paginated.sort((a, b) => {
                a = getSortItem(a, sorting.value.sortBy!);
                b = getSortItem(b, sorting.value.sortBy!);

                if (a < b) {
                    return sorting.value.sortingOrder === 'asc' ? -1 : 1;
                }
                if (a > b) {
                    return sorting.value.sortingOrder === 'asc' ? 1 : -1;
                }
                return 0;
            });
        }

        return paginated;
    });

    fetch();

    return {
        isLoading,
        tenants,
        fetch,
        async add(tenant: Omit<Tenant, 'id' | 'createdAt'>) {
            isLoading.value = true;
            await tenantsStore.add(tenant);
            await fetch();
            isLoading.value = false;
        },

        async update(tenant: Tenant) {
            isLoading.value = true;
            console.log("tenant:...........",tenant)
            await tenantsStore.update(tenant);
            await fetch();
            isLoading.value = false;
        },

        async remove(tenant: Tenant) {
            isLoading.value = true;
            await tenantsStore.remove(tenant);
            await fetch();
            isLoading.value = false;
        },
        pagination,
        sorting,
    };
};
