<script setup lang="ts">
// This component is not used for VehicleType since VehicleType doesn't have status
// Keeping for potential future use or removal
import {VaBadge} from "vuestic-ui";

defineProps({
  status: {
    type: String,
    required: true,
  },
})

const badgeColorMap: Record<string, string> = {
  'active': 'success',
  'inactive': 'secondary',
}
</script>

<template>
  <VaBadge square :color="badgeColorMap[$props.status] || 'primary'" :text="$props.status.toUpperCase()" />
</template>
