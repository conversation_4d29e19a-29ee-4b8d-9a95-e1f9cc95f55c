<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {EmptyTenant, Tenant} from '../types'
import {VaButton, VaForm, VaInput} from "vuestic-ui";

const props = defineProps<{
    tenant: Tenant | null
    saveButtonLabel: string
}>()

const emit = defineEmits<{
    (event: 'save', tenant: Tenant): void
    (event: 'close'): void
}>()

const defaultNewTenant: EmptyTenant = {
    name: '',
}

const newTenant = ref<Tenant | EmptyTenant>({...defaultNewTenant})

const isFormHasUnsavedChanges = computed(() => {
    if (!props.tenant) {
        // Creating new - check if name is different from empty
        return newTenant.value.name !== defaultNewTenant.name
    }
    // Editing existing - check if name is different from original
    return newTenant.value.name !== props.tenant.name
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.tenant,
    () => {
        if (!props.tenant) {
            newTenant.value = {...defaultNewTenant}
            return
        }

        newTenant.value = {
            ...props.tenant,
            name: props.tenant.name,
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'

const handleSave = () => {
    console.log('handleSave called')
    console.log('newTenant.value:', newTenant.value)
    console.log('props.tenant:', props.tenant)

    // Create the object to emit
    const tenantToSave = props.tenant
        ? {...props.tenant, name: newTenant.value.name} // Editing: preserve all fields, update name
        : {name: newTenant.value.name} // Creating: only name

    console.log('Emitting:', tenantToSave)
    emit('save', tenantToSave as Tenant)
}
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <VaInput v-model="newTenant.name" label="Vehicle Type Name" :rules="[required]"/>
        <div class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
    </VaForm>
</template>
