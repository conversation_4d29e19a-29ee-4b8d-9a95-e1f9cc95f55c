<script setup lang="ts">
import {computed, ref, watch} from 'vue'
import {EmptyTenant, Tenant} from '../types'
import {VaButton, VaForm, VaInput} from "vuestic-ui";

const props = defineProps<{
    tenant: Tenant | null
    saveButtonLabel: string
}>()

const emit = defineEmits<{
    (event: 'save', tenant: Tenant): void
    (event: 'close'): void
}>()

const defaultNewTenant: EmptyTenant = {
    name: '',
    code: '',
    email: '',
    phone: '',
    address: '',
    website: '',
    logoUrl: '',
    isActive: true,
    subscriptionPlan: '',
    subscriptionStartDate: '',
    subscriptionEndDate: '',
}

const newTenant = ref<Tenant | EmptyTenant>({...defaultNewTenant})

const isFormHasUnsavedChanges = computed(() => {
    if (!props.tenant) {
        // Creating new - check if any field is different from empty
        return Object.keys(defaultNewTenant).some(key =>
            newTenant.value[key as keyof EmptyTenant] !== defaultNewTenant[key as keyof EmptyTenant]
        )
    }
    // Editing existing - check if any field is different from original
    return Object.keys(newTenant.value).some(key =>
        newTenant.value[key as keyof Tenant] !== props.tenant![key as keyof Tenant]
    )
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.tenant,
    () => {
        if (!props.tenant) {
            newTenant.value = {...defaultNewTenant}
            return
        }

        newTenant.value = {
            ...props.tenant,
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'

const handleSave = () => {
    // Create the object to emit
    const tenantToSave = props.tenant
        ? {...props.tenant, ...newTenant.value} // Editing: preserve all fields, update with form values
        : newTenant.value // Creating: use all form values

    emit('save', tenantToSave as Tenant)
}
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <VaInput v-model="newTenant.name" label="Tenant Name" :rules="[required]"/>
        <VaInput v-model="newTenant.code" label="Tenant Code" :rules="[required]"/>
        <VaInput v-model="newTenant.email" label="Email" type="email" :rules="[required]"/>
        <VaInput v-model="newTenant.phone" label="Phone" :rules="[required]"/>
        <VaInput v-model="newTenant.address" label="Address" :rules="[required]"/>
        <VaInput v-model="newTenant.website" label="Website (Optional)"/>
        <VaInput v-model="newTenant.logoUrl" label="Logo URL (Optional)"/>
        <VaInput v-model="newTenant.subscriptionPlan" label="Subscription Plan (Optional)"/>
        <VaInput v-model="newTenant.subscriptionStartDate" label="Subscription Start Date (Optional)" type="date"/>
        <VaInput v-model="newTenant.subscriptionEndDate" label="Subscription End Date (Optional)" type="date"/>
        <div class="flex justify-end flex-col-reverse sm:flex-row mt-4 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
    </VaForm>
</template>
