<script setup lang="ts">
import {ref} from 'vue'
import {useModal, useToast, VaButton, VaButtonToggle, VaCard, VaCardContent, VaModal} from 'vuestic-ui'
import {Tenant} from './types'
import {useLocalStorage} from '@vueuse/core'
import {useTenants} from './composables/useTenants'
import TenantCards from './widgets/TenantCards.vue'
import TenantsTable from './widgets/TenantsTable.vue'
import EditTenantForm from './widgets/EditTenantForm.vue'

const doShowAsCards = useLocalStorage('tenants-view', false)

const {tenants, add, update, remove, isLoading,  pagination, sorting} = useTenants()

const tenantToEdit = ref<Tenant | null>(null)
const doShowTenantFormModal = ref(false)

const editTenant = (tenant: Tenant) => {
    tenantToEdit.value = tenant
    doShowTenantFormModal.value = true
}

const createNewTenant = () => {
    tenantToEdit.value = null
    doShowTenantFormModal.value = true
}

const {init: notify} = useToast()

const onTenantSaved = async (tenant: Tenant) => {
    doShowTenantFormModal.value = false
    if ('id' in tenant && tenant.id) {
        await update(tenant as Tenant)
        notify({
            message: 'Tenant updated',
            color: 'success',
        })
    } else {
        await add(tenant as Tenant)
        notify({
            message: 'Tenant created',
            color: 'success',
        })
    }
}

const {confirm} = useModal()

const onTenantDeleted = async (tenant: Tenant) => {
    const response = await confirm({
        title: 'Delete Tenant',
        message: `Are you sure you want to delete tenant "${tenant.name}"?`,
        okText: 'Delete',
        size: 'small',
        maxWidth: '380px',
    })

    if (!response) {
        return
    }

    await remove(tenant)
    notify({
        message: 'Tenant deleted',
        color: 'success',
    })
}

const editFormRef = ref()

const beforeEditFormModalClose = async (hide: () => unknown) => {
    if (editFormRef.value.isFormHasUnsavedChanges) {
        const agreed = await confirm({
            maxWidth: '380px',
            message: 'Form has unsaved changes. Are you sure you want to close it?',
            size: 'small',
        })
        if (agreed) {
            hide()
        }
    } else {
        hide()
    }
}
</script>

<template>
    <h6 class="page-title">Tenants</h6>

    <VaCard>
        <VaCardContent>
            <div class="flex flex-col md:flex-row gap-2 mb-2 justify-between">
                <div class="flex flex-col md:flex-row gap-2 justify-start">
                    <VaButtonToggle
                        v-model="doShowAsCards"
                        color="background-element"
                        border-color="background-element"
                        :options="[{ label: 'Cards', value: true }, { label: 'Table', value: false },]"
                    />
                </div>
                <VaButton icon="add" @click="createNewTenant">Tenant</VaButton>
            </div>

            <TenantCards
                v-if="doShowAsCards"
                :tenants="tenants"
                :loading="isLoading"
                @edit="editTenant"
                @delete="onTenantDeleted"
            />
            <TenantsTable
                v-else
                v-model:sort-by="sorting.sortBy"
                v-model:sorting-order="sorting.sortingOrder"
                v-model:pagination="pagination"
                :tenants="tenants"
                :loading="isLoading"
                @edit="editTenant"
                @delete="onTenantDeleted"
            />
        </VaCardContent>

        <VaModal
            v-slot="{ cancel, ok }"
            v-model="doShowTenantFormModal"
            size="small"
            mobile-fullscreen
            close-button
            stateful
            hide-default-actions
            :before-cancel="beforeEditFormModalClose"
        >
            <h1 v-if="tenantToEdit === null" class="va-h5 mb-4">Add Tenant</h1>
            <h1 v-else class="va-h5 mb-4">Edit Tenant</h1>
            <EditTenantForm
                ref="editFormRef"
                :tenant="tenantToEdit"
                :save-button-label="tenantToEdit === null ? 'Add' : 'Save'"
                @close="cancel"
                @save="(tenant) => {
                    onTenantSaved(tenant)
                    ok()
                }"
            />
        </VaModal>
    </VaCard>
</template>
