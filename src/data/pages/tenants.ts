import api from '../../services/api'
import {Tenant} from '../../pages/tenants/types'

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    orderBy: 'name' | 'updatedAt' | 'createdAt'
    sort: 'ASC' | 'DESC' | null
}

export const getTenants = async (options: Partial<Sorting> & Pagination) => {
    console.log("Options received",options)
    const response = await fetch(api.allTenants(options)).then((r) => r.json())
    return {
        data: response.data || [],
        pagination: response.meta || {page: 1, perPage: 10, total: 0},
    }
}

export const addTenant = async (tenant:Tenant) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')
    const response = await fetch(api.allTenants(), {
        method: 'POST',
        body: JSON.stringify(tenant),
        headers
    }).then((r) => r.json())
    return response.data
}

export const updateTenant = async (tenant: Tenant) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')
    const {id,createdAt, updatedAt, ...payload} = tenant
    const response = await fetch(api.tenant(id), {
        method: 'PATCH',
        body: JSON.stringify(payload),
        headers
    }).then((r) => r.json())
    return response.data
}

export const removeTenant = async (tenant: Tenant) => {
    const response = await fetch(api.tenant(tenant.id), {method: 'DELETE'})
    return response.ok
}
