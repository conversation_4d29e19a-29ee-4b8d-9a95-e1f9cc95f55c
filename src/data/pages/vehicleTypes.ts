import api from '../../services/api'
import { VehicleType } from '../../pages/vehicle-types/types'

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: 'name' | 'updatedAt' | 'createdAt'
    sortingOrder: 'ASC' | 'DESC' | null
}

export const getVehicleTypes = async (options: Partial<Sorting> & Pagination) => {
    const vehicleTypes: VehicleType[] = await fetch(api.allVehicleTypes()).then((r) => r.json())
    return {
        data: vehicleTypes.data,
        pagination: vehicleTypes.meta,
    }
}

export const addVehicleType = async (vehicleType: Omit<VehicleType, 'id' | 'createdAt'>) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')
    return fetch(api.allVehicleTypes(), {
        method: 'POST',
        body: JSON.stringify(vehicleType),
        headers
    }).then((r) => r.json())
}

export const updateVehicleType = async (vehicleType: Omit<VehicleType, 'createdAt'>) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')

    return fetch(api.vehicleType(vehicleType.id), {
        method: 'PUT',
        body: JSON.stringify(vehicleType),
        headers
    }).then((r) => r.json())
}

export const removeVehicleType = async (vehicleType: VehicleType) => {
    return fetch(api.vehicleType(vehicleType.id), {method: 'DELETE'})
}
