import {
    BadRequestException,
    ConflictException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
    Logger
} from '@nestjs/common';
import {CreateTenantDto} from './dto/create-tenant.dto';
import {TenantResponseDto} from "./dto/tenant-response.dto";
import {InjectRepository} from "@nestjs/typeorm";
import {Repository} from "typeorm";
import {Tenant} from "./entities/tenant.entity";
import {validate as isUUID} from 'uuid';
import {plainToInstance} from "class-transformer";
import {UpdateTenantDto} from "./dto/update-tenant.dto";
import {format} from 'date-fns';
import { ResponseUtil } from '../common/utils/response.util';
import { ApiResponse, PaginatedApiResponse } from '../common/interfaces/api-response.interface';

@Injectable()
export class TenantService {
    private readonly logger = new Logger(TenantService.name);

    constructor(
        @InjectRepository(Tenant) private readonly tenantRepository: Repository<Tenant>,
    ) {
    }

    async create(createTenantDto: CreateTenantDto): Promise<{
        data: TenantResponseDto;
        message: string;
    }> {
        const {name} = createTenantDto;

        // Fallback required field validation
        const requiredFields: Record<string, string> = {
            name: 'Name',
            email: 'Email',
            phone: 'Phone number',
        };

        for (const [field, label] of Object.entries(requiredFields)) {
            if (!createTenantDto[field]) {
                throw new BadRequestException(`Missing required field: ${label}`);
            }
        }

        // Check if tenant already exists
        const existingTenant = await this.tenantRepository.findOne({
            where: {name},
        });

        if (existingTenant) {
            throw new ConflictException('A tenant with this name already exists');
        }

        // Generate initials once
        const initials = name
            .split(' ')
            .map((word) => word.charAt(0).toUpperCase())
            .join('');

        let savedTenant;
        const maxRetries = 3;
        let attempt = 0;

        while (attempt < maxRetries) {
            const timestamp = format(new Date(), 'yyyyMMddHHmmssSSS'); // adds milliseconds
            const code = `${initials}${timestamp}`;

            const tenant = this.tenantRepository.create({...createTenantDto, code});

            try {
                savedTenant = await this.tenantRepository.save(tenant);
                break; // success
            } catch (error) {
                // Unique violation (e.g., code)
                if (error.code === '23505') {
                    attempt++;
                    continue;
                }

                // NOT NULL violation
                if (error.code === '23502') {
                    const columnName = error.column || 'a required field';
                    throw new BadRequestException(`Missing required field: ${columnName}`);
                }

                console.error('Unexpected error while saving tenant:', error);
                throw new InternalServerErrorException('Unexpected error while creating tenant.');
            }
        }

        if (!savedTenant) {
            throw new ConflictException('Could not generate a unique tenant code. Please try again.');
        }

        return {
            data: plainToInstance(TenantResponseDto, savedTenant, {
                excludeExtraneousValues: true,
            }),
            message: 'The tenant was successfully created.',
        };
    }


    async findAll(
        page: number,
        perPage: number,
        sort: string,
        order: 'ASC' | 'DESC',
    ): Promise<{
        data: TenantResponseDto[];
        meta: {
            total: number;
            page: number;
            perPage: number;
            totalPages: number;
        };
        message: string;
    }> {
        const [records, total] = await this.tenantRepository.findAndCount({
            order: {[sort]: order.toUpperCase() === 'DESC' ? 'DESC' : 'ASC'},
            skip: (page - 1) * perPage,
            take: perPage,
        });

        const data = plainToInstance(TenantResponseDto, records, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            meta: {
                total,
                page,
                perPage,
                totalPages: Math.ceil(total / perPage),
            },
            message: 'Tenants retrieved successfully',
        };
    }

    async findOneByIdOrName(query: string): Promise<{
        data: TenantResponseDto;
        message: string;
    }> {
        let tenant: Tenant | null = null;

        if (isUUID(query)) {
            tenant = await this.tenantRepository.findOne({
                where: {id: query},
            });
        }

        if (!tenant) {
            tenant = await this.tenantRepository.findOne({
                where: {name: query},
            });
        }

        if (!tenant) {
            throw new NotFoundException(
                `Tenant with ID or name "${query}" not found`,
            );
        }

        const data = plainToInstance(TenantResponseDto, tenant, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The tenant retrieved successfully.',
        };
    }

    async update(
        identifier: string,
        updateTenantDto: UpdateTenantDto,
    ): Promise<{ data: TenantResponseDto; message: string }> {
        const isUuid = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$/.test(identifier);

        const tenant = await this.tenantRepository.findOne({
            where: isUuid ? {id: identifier} : {name: identifier},
        });

        if (!tenant) {
            throw new NotFoundException('Tenant with ID or name not found.');
        }

        if (
            updateTenantDto.name &&
            updateTenantDto.name !== tenant.name
        ) {
            const existing = await this.tenantRepository.findOne({
                where: {name: updateTenantDto.name},
            });

            if (existing) {
                throw new ConflictException('Tenant with the provided name already exists.');
            }
        }

        Object.assign(tenant, updateTenantDto);
        const updated = await this.tenantRepository.save(tenant);

        const data = plainToInstance(TenantResponseDto, updated, {
            excludeExtraneousValues: true,
        });

        return {
            data,
            message: 'The tenant successfully updated.',
        };
    }

    async remove(id: string): Promise<{ message: string }> {
        const tenant = await this.tenantRepository.findOne({where: {id}});

        if (!tenant) {
            throw new NotFoundException(`Tenant with id ${id} not found`);
        }

        await this.tenantRepository.remove(tenant);

        return {message: 'The tenant successfully deleted.'};
    }
}
