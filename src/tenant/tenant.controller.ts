import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query, UseFilters, Logger} from '@nestjs/common';
import {TenantService} from './tenant.service';
import {CreateTenantDto} from './dto/create-tenant.dto';
import {UpdateTenantDto} from './dto/update-tenant.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {Tenant} from "./entities/tenant.entity";
import { HttpExceptionFilter } from '../common/filters/http-exception.filter';

@ApiTags('Tenant')
@Controller('tenant')
export class TenantController {
    constructor(private readonly tenantService: TenantService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new tenant'})
    @ApiResponse({status: 201, description: 'The tenant successfully created.', type: Tenant})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 409, description: 'Tenant already exists.'})
    create(@Body() createTenantDto: CreateTenantDto) {
        return this.tenantService.create(createTenantDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all tenant'})
    @ApiResponse({
        status: 200,
        description: 'The tenant retrieved successfully.',
        type: Tenant,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Tenant with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.tenantService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve tenant by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Tenant ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The tenant retrieved successfully.',
        type: Tenant,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Tenant with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.tenantService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update tenant'})
    @ApiParam({
        name: 'identifier',
        description: 'Tenant ID (UUID)',
        type: String,
        example: 'ac1c78df-73f0-486f-bc83-a7c9b275199d'
    })
    @ApiResponse({
        status: 200,
        description: 'The tenant successfully updated.',
        type: Tenant
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Tenant with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Tenant with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateTenantDto: UpdateTenantDto
    ) {
        return this.tenantService.update(identifier, updateTenantDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete tenant'})
    @ApiParam({
        name: 'id',
        description: 'Tenant ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The tenant successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Tenant with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.tenantService.remove(id);
    }

}
