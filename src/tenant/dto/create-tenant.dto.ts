import {ApiProperty, ApiPropertyOptional} from '@nestjs/swagger';
import {IsBoolean, IsDate, IsEmail, IsNotEmpty, IsOptional, IsString, IsUrl,} from 'class-validator';
import {Type} from 'class-transformer';

export class CreateTenantDto {
    @ApiProperty({description: 'Name of the tenant organization or business'})
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({description: 'Tenant contact email address'})
    @IsEmail()
    @IsNotEmpty()
    email: string;

    @ApiProperty({description: 'Primary contact phone number for the tenant'})
    @IsString()
    @IsNotEmpty()
    phone: string;

    @ApiProperty({description: 'Physical or mailing address of the tenant'})
    @IsString()
    @IsNotEmpty()
    address: string;

    @ApiPropertyOptional({description: 'Tenant website or landing page'})
    @IsOptional()
    @IsUrl()
    website?: string;

    @ApiPropertyOptional({description: 'Logo or branding image of the tenant'})
    @IsOptional()
    @IsUrl()
    logoUrl?: string;

    @ApiPropertyOptional({description: 'Status flag to indicate if the tenant is active', default: true})
    @IsOptional()
    @IsBoolean()
    isActive?: boolean;

    @ApiPropertyOptional({description: 'Subscription or billing plan name'})
    @IsOptional()
    @IsString()
    subscriptionPlan?: string;

    @ApiPropertyOptional({description: 'Date when the current subscription started'})
    @IsOptional()
    @Type(() => Date)
    @IsDate()
    subscriptionStartDate?: Date;

    @ApiPropertyOptional({description: 'Date when the current subscription ends'})
    @IsOptional()
    @Type(() => Date)
    @IsDate()
    subscriptionEndDate?: Date;
}
