import {defineStore} from 'pinia'
import {addTenant, getTenants, Pagination, removeTenant, Sorting, updateTenant} from '../data/pages/tenants'
import {Tenant} from '../pages/tenants/types'

export const useTenantsStore = defineStore('tenants', {
    state: () => {
        return {
            items: [] as Tenant[],
            pagination: {
                page: 1,
                perPage: 10,
                total: 0,
            } as Pagination,
        }
    },

    actions: {
        async getAll(options: { pagination: Pagination; sorting?: Sorting }) {
            const {data, pagination} = await getTenants({
                ...options.sorting,
                ...options.pagination,
            })
            this.items = data
            this.pagination = pagination
        },

        async add(tenant: Omit<Tenant, 'id' | 'createdAt'>) {
            const newTenant = await addTenant(tenant)
            this.items.push(newTenant)
        },

        async update(tenant: Tenant) {
            console.log("Store Data received",tenant)
            const updatedTenant = await updateTenant(tenant)
            const index = this.items.findIndex(({id}) => id === tenant.id)
            this.items.splice(index, 1, updatedTenant)
        },

        async remove(tenant: Tenant) {
            const isRemoved = await removeTenant(tenant)

            if (isRemoved) {
                const index = this.items.findIndex(({id}) => id === tenant.id)
                this.items.splice(index, 1)
            }
        },
    },
})
